<template>
  <section class="w-full bg-gradient-sport dark:bg-gradient-sport-dark relative overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-10">
      <div class="absolute inset-0 bg-pattern-dots"></div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 lg:py-24 relative">
      <div class="max-w-4xl mx-auto text-center text-white">
        <!-- Header -->
        <div
          class="mb-8"
          data-aos="fade-up"
          data-aos-duration="600"
        >
          <h2 class="text-3xl lg:text-4xl font-bold mb-4">
            Tham gia
            <span class="text-white/90">Cộng đồng thể thao</span>
          </h2>
          <p class="text-xl opacity-90 leading-relaxed">
            Đ<PERSON>ng ký nhận bản tin để nhận ưu đãi độ<PERSON> quyền, mẹo thể dục và truy cập sớm vào sản phẩm mới.
          </p>
        </div>

        <!-- Newsletter Form -->
        <div
          class="max-w-md mx-auto mb-8"
          data-aos="fade-up"
          data-aos-duration="600"
          data-aos-delay="200"
        >
          <div class="flex flex-col sm:flex-row gap-3">
            <div class="flex-1">
              <n-input
                v-model:value="email"
                type="text"
                placeholder="Nhập địa chỉ email của bạn"
                size="large"
                class="newsletter-input"
                :class="{ 'error': emailError }"
                @keyup.enter="handleSubscribe"
              >
                <template #prefix>
                  <n-icon size="18" class="text-light-text-muted dark:text-dark-text-muted">
                    <component :is="mailIcon" />
                  </n-icon>
                </template>
              </n-input>
              <div v-if="emailError" class="text-red-300 text-sm mt-1 text-left">
                {{ emailError }}
              </div>
            </div>
            <n-button
              type="primary"
              size="large"
              class="bg-white text-light-accent-sport hover:bg-gray-100 font-semibold px-8"
              :loading="isSubscribing"
              @click="handleSubscribe"
            >
              <template #icon>
                <n-icon size="18">
                  <component :is="sendIcon" />
                </n-icon>
              </template>
              Đăng ký
            </n-button>
          </div>
        </div>

        <!-- Benefits -->
        <div
          class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8"
          data-aos="fade-up"
          data-aos-duration="600"
          data-aos-delay="400"
        >
          <div
            v-for="(benefit, index) in benefits"
            :key="benefit.id"
            class="flex items-center justify-center gap-3 text-white/90"
            :data-aos="'fade-up'"
            :data-aos-duration="400"
            :data-aos-delay="500 + (index * 100)"
          >
            <n-icon size="20" class="text-white">
              <component :is="benefit.icon" />
            </n-icon>
            <span class="text-sm font-medium">{{ benefit.text }}</span>
          </div>
        </div>

        <!-- Privacy Notice -->
        <p
          class="text-sm text-white/70 leading-relaxed"
          data-aos="fade-up"
          data-aos-duration="600"
          data-aos-delay="600"
        >
          Bằng cách đăng ký, bạn đồng ý với
          <a href="#" class="underline hover:text-white transition-colors">Điều khoản dịch vụ</a>
          và
          <a href="#" class="underline hover:text-white transition-colors">Chính sách bảo mật</a> của chúng tôi.
          Hủy đăng ký bất cứ lúc nào.
        </p>

        <!-- Success Message -->
        <div 
          v-if="showSuccess"
          class="mt-6 p-4 bg-white/20 backdrop-blur-sm rounded-lg border border-white/30"
        >
          <div class="flex items-center justify-center gap-2 text-white">
            <n-icon size="20">
              <component :is="checkmarkIcon" />
            </n-icon>
            <span class="font-medium">Cảm ơn bạn đã đăng ký! Vui lòng kiểm tra email để xác nhận.</span>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref, markRaw } from 'vue'
import { NInput, NButton, NIcon } from 'naive-ui'
import {
  MailOutline,
  SendOutline,
  CheckmarkCircleOutline,
  GiftOutline,
  FlashOutline,
  StarOutline
} from '@vicons/ionicons5'

// State
const email = ref('')
const emailError = ref('')
const isSubscribing = ref(false)
const showSuccess = ref(false)

// Icons (markRaw for performance)
const mailIcon = markRaw(MailOutline)
const sendIcon = markRaw(SendOutline)
const checkmarkIcon = markRaw(CheckmarkCircleOutline)

// Benefits data
const benefits = ref([
  {
    id: 1,
    text: 'Ưu đãi độc quyền',
    icon: markRaw(GiftOutline)
  },
  {
    id: 2,
    text: 'Truy cập sớm',
    icon: markRaw(FlashOutline)
  },
  {
    id: 3,
    text: 'Mẹo thể dục',
    icon: markRaw(StarOutline)
  }
])

// Methods
const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

const handleSubscribe = async () => {
  // Reset states
  emailError.value = ''
  
  // Validate email
  if (!email.value) {
    emailError.value = 'Email là bắt buộc'
    return
  }

  if (!validateEmail(email.value)) {
    emailError.value = 'Vui lòng nhập địa chỉ email hợp lệ'
    return
  }

  // Simulate subscription process
  isSubscribing.value = true
  
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    // Show success message
    showSuccess.value = true
    email.value = ''
    
    // Hide success message after 5 seconds
    setTimeout(() => {
      showSuccess.value = false
    }, 5000)
    
    console.log('Đăng ký bản tin thành công')
  } catch (error) {
    emailError.value = 'Đăng ký thất bại. Vui lòng thử lại.'
    console.error('Lỗi đăng ký bản tin:', error)
  } finally {
    isSubscribing.value = false
  }
}
</script>
